package com.example;

import com.google.javascript.jscomp.SourceFile;
import com.google.javascript.jscomp.parsing.parser.Parser;
import com.google.javascript.jscomp.parsing.parser.util.ErrorReporter;
import com.google.javascript.jscomp.parsing.parser.util.SourcePosition;
import com.google.javascript.jscomp.parsing.parser.util.SourcePosition;
import com.google.javascript.jscomp.parsing.parser.trees.ProgramTree;

/**
 * Simple demo to test Google Closure Compiler Parser.parseProgram() method
 */
public class ClosureParserDemo {

    public static void main(String[] args) {
        ClosureParserDemo demo = new ClosureParserDemo();

        // Test with valid JavaScript
        String validJs = "const x = 42; console.log(x);";
        System.out.println("=== Testing Valid JavaScript ===");
        System.out.println("Code: " + validJs);
        demo.testParseProgram(validJs);

        System.out.println("\n" + "=".repeat(50) + "\n");

        // Test with invalid JavaScript
        String invalidJs = "const x = ; // syntax error";
        System.out.println("=== Testing Invalid JavaScript ===");
        System.out.println("Code: " + invalidJs);
        demo.testParseProgram(invalidJs);
    }
    
    /**
     * Parse JavaScript code string and return the AST root node using direct Parser.
     *
     * @param jsCode JavaScript source code as string
     * @return Root node of the AST, or null if parsing failed
     */
    public Node parseProgram(String jsCode) {
        try {
            // Create source file from the JavaScript code string
            SourceFile sourceFile = SourceFile.fromCode("input.js", jsCode);

            // Create simple error reporter
            ErrorReporter reporter = new ErrorReporter() {
                @Override
                public void reportError(String message) {
                    System.err.println("Parser Error: " + message);
                }

                @Override
                public void reportWarning(String message) {
                    System.err.println("Parser Warning: " + message);
                }
            };

            // Create parser configuration using the static method
            Config parserConfig = Config.builder()
                .setLanguageMode(Config.LanguageMode.ECMASCRIPT_2015)
                .setJsDocParsing(Config.JsDocParsing.INCLUDE_DESCRIPTIONS_NO_WHITESPACE)
                .setRunMode(Config.RunMode.KEEP_GOING)
                .setStrictMode(true)
                .build();

            // Create the parser instance
            Parser parser = new Parser(parserConfig, reporter, sourceFile);

            // Parse the program - this returns a ProgramTree
            ProgramTree programTree = parser.parseProgram();

            // For now, let's use the Compiler approach to get a Node
            // since the direct parser returns a different AST format
            return parseUsingCompiler(jsCode);

        } catch (Exception e) {
            System.err.println("Parsing failed with exception: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Fallback method using Compiler for Node-based AST.
     */
    private Node parseUsingCompiler(String jsCode) {
        try {
            // Create a new Closure Compiler instance
            Compiler compiler = new Compiler();

            // Set up compiler options
            CompilerOptions options = new CompilerOptions();
            options.setParseJsDocDocumentation(Config.JsDocParsing.INCLUDE_DESCRIPTIONS_NO_WHITESPACE);
            options.setPreserveTypeAnnotations(true);

            // Create source file from the JavaScript code string
            SourceFile sourceFile = SourceFile.fromCode("input.js", jsCode);

            // Parse the code (no external dependencies)
            compiler.compile(Arrays.asList(), Arrays.asList(sourceFile), options);

            // Check for parsing errors
            List<JSError> errors = compiler.getErrors();
            if (!errors.isEmpty()) {
                System.err.println("Parsing errors:");
                for (JSError error : errors) {
                    System.err.println("  " + error.toString());
                }
                return null;
            }

            // Get the AST root node
            Node root = compiler.getRoot();
            return root;

        } catch (Exception e) {
            System.err.println("Compiler parsing failed: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Safely get string value from a node, returning null if not available.
     * 
     * @param node The node to get string from
     * @return String value or null if not available
     */
    private String safeGetString(Node node) {
        try {
            return node.getString();
        } catch (ClassCastException e) {
            // Node doesn't have a string value
            return null;
        }
    }
    
    /**
     * Recursively analyze and print the AST structure.
     * 
     * @param node Current AST node
     * @param depth Current depth for indentation
     */
    private void analyzeAST(Node node, int depth) {
        if (node == null) return;
        
        String indent = "  ".repeat(depth);
        Token tokenType = node.getToken();
        String nodeString = safeGetString(node);
        
        System.out.println(indent + "Node: " + tokenType + 
                          (nodeString != null ? " [" + nodeString + "]" : ""));
        
        // Recursively process child nodes
        for (Node child : node.children()) {
            analyzeAST(child, depth + 1);
        }
    }
    
    /**
     * Find and print all function declarations in the AST.
     * 
     * @param node AST node to search
     */
    private void findFunctionDeclarations(Node node) {
        if (node == null) return;
        
        if (node.getToken() == Token.FUNCTION) {
            Node nameNode = node.getFirstChild();
            String functionName = nameNode != null ? safeGetString(nameNode) : null;
            if (functionName == null) {
                functionName = "<anonymous>";
            }
            System.out.println("  Found function: " + functionName);
            
            // Get parameters
            Node paramList = nameNode != null ? nameNode.getNext() : null;
            if (paramList != null && paramList.getToken() == Token.PARAM_LIST) {
                System.out.print("    Parameters: ");
                for (Node param : paramList.children()) {
                    String paramName = safeGetString(param);
                    if (paramName != null) {
                        System.out.print(paramName + " ");
                    }
                }
                System.out.println();
            }
        }
        
        // Recursively search child nodes
        for (Node child : node.children()) {
            findFunctionDeclarations(child);
        }
    }
    
    /**
     * Find and print all variable declarations in the AST.
     * 
     * @param node AST node to search
     */
    private void findVariableDeclarations(Node node) {
        if (node == null) return;
        
        if (node.getToken() == Token.VAR || 
            node.getToken() == Token.LET || 
            node.getToken() == Token.CONST) {
            
            String declarationType = node.getToken().toString().toLowerCase();
            
            for (Node child : node.children()) {
                if (child.getToken() == Token.NAME) {
                    String varName = safeGetString(child);
                    if (varName != null) {
                        System.out.println("  Found " + declarationType + " declaration: " + varName);
                    }
                }
            }
        }
        
        // Recursively search child nodes
        for (Node child : node.children()) {
            findVariableDeclarations(child);
        }
    }
    
    /**
     * Find and print all class declarations in the AST.
     * 
     * @param node AST node to search
     */
    private void findClassDeclarations(Node node) {
        if (node == null) return;
        
        if (node.getToken() == Token.CLASS) {
            Node nameNode = node.getFirstChild();
            String className = nameNode != null ? safeGetString(nameNode) : null;
            if (className == null) {
                className = "<anonymous>";
            }
            System.out.println("  Found class: " + className);
            
            // Find methods in the class
            Node classMembers = node.getLastChild();
            if (classMembers != null) {
                for (Node member : classMembers.children()) {
                    if (member.getToken() == Token.MEMBER_FUNCTION_DEF) {
                        Node methodName = member.getFirstChild();
                        if (methodName != null) {
                            String methodNameStr = safeGetString(methodName);
                            if (methodNameStr != null) {
                                System.out.println("    Method: " + methodNameStr);
                            }
                        }
                    }
                }
            }
        }
        
        // Recursively search child nodes
        for (Node child : node.children()) {
            findClassDeclarations(child);
        }
    }
}